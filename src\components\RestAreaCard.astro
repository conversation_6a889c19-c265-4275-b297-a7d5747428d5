---
import { getLangFromUrl, useTranslations } from '../i18n/utils';

export interface RestArea {
  id: string;
  slug?: string; // Keep for backward compatibility
  data: {
    title: string;
    description_short: string;
    address_line: string;
    rating?: number;
    highway_tag: string;
    featured_image: string;
    location_path: string;
    amenities: {
      toilets: boolean;
      wifi: boolean;
      fuel_station: boolean;
      restaurant: boolean;
      shop: boolean;
      playground: boolean;
      showers: boolean;
      car_wash: boolean;
      ev_charging: boolean;
      security: boolean;
      cctv: boolean;
      lighting: boolean;
      accommodation: boolean;
    };
    work_hours: string;
  };
}

export interface Props {
  restArea: RestArea;
  hierarchicalUrl?: string;
}

const { restArea, hierarchicalUrl } = Astro.props;

// Get current language and translation function
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

const currentLocale = Astro.currentLocale; // Get current locale of the page rendering the card
let cleanSlug = '';
if (restArea.id.startsWith('en/')) {
  cleanSlug = restArea.id.substring(3).replace(/\.md$/, '');
} else if (restArea.id.startsWith('pl/')) {
  cleanSlug = restArea.id.substring(3).replace(/\.md$/, '');
} else {
  // Fallback for older structures or if no locale prefix in id, though less likely with new structure
  cleanSlug = restArea.id.replace(/\.md$/, '');
}

// Generate hierarchical URL based on location_path and locale
let cardLink = '';
if (hierarchicalUrl) {
  // Use provided hierarchical URL
  cardLink = hierarchicalUrl;
} else {
  // Generate hierarchical URL from location_path
  const locationPath = restArea.data.location_path;
  if (locationPath && locationPath.includes('/')) {
    // Extract location parts after "poland/"
    const locationParts = locationPath.replace(/^poland\//, '');

    if (currentLocale === 'pl') {
      cardLink = `/pl/polska/${locationParts}/${cleanSlug}`;
    } else {
      cardLink = `/poland/${locationParts}/${cleanSlug}`;
    }
  } else {
    // Fallback to flat URLs
    if (currentLocale === 'pl') {
      cardLink = `/pl/rest-areas/${cleanSlug}`;
    } else {
      cardLink = `/rest-areas/${cleanSlug}`;
    }
  }
}

// Parse work hours
function getOperatingStatus(workHours: string) {
  try {
    const hours = JSON.parse(workHours);
    const today = new Date().toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
    const todayHours = hours[today] || hours.monday;

    if (todayHours === "00:00-24:00") {
      return { status: "Open 24/7", class: "text-green-600 dark:text-green-400" };
    } else if (todayHours) {
      return { status: `Open ${todayHours}`, class: "text-blue-600 dark:text-blue-400" };
    }
    return { status: "Hours vary", class: "text-secondary-600 dark:text-secondary-400" };
  } catch {
    return { status: "Hours vary", class: "text-secondary-600 dark:text-secondary-400" };
  }
}

const operatingStatus = getOperatingStatus(restArea.data.work_hours);

// Get key amenities to display with localized labels
const keyAmenityKeys = [
  { key: 'fuel_station', icon: '⛽' },
  { key: 'restaurant', icon: '🍽️' },
  { key: 'toilets', icon: '🚻' },
  { key: 'wifi', icon: '📶' },
  { key: 'ev_charging', icon: '🔌' },
  { key: 'accommodation', icon: '🏨' }
];

const keyAmenities = keyAmenityKeys
  .filter(amenity => restArea.data.amenities[amenity.key as keyof typeof restArea.data.amenities])
  .map(amenity => ({
    ...amenity,
    label: t(`amenities.${amenity.key}`)
  }));
---

<a
  href={hierarchicalUrl || cardLink}
  class="block"
  data-rest-area-card
  data-rest-area-id={cleanSlug}
  data-rest-area-name={restArea.data.title}
  data-highway={restArea.data.highway_tag}
  data-region={restArea.data.address_line.split(',')[1]?.trim() || 'Unknown'}
>
  <article class="card group hover:scale-105 transition-transform duration-300">
    <div class="relative overflow-hidden">
      <img
        src={restArea.data.featured_image}
        alt={restArea.data.title}
        class="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
        loading="lazy"
      />
      <div class="absolute top-3 left-3">
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200">
          {restArea.data.highway_tag.startsWith('A') ? `${t('restArea.highway')} ${restArea.data.highway_tag}` :
           restArea.data.highway_tag.startsWith('S') ? `${t('restArea.expressway')} ${restArea.data.highway_tag}` :
           restArea.data.highway_tag}
        </span>
      </div>
      {/* {restArea.data.rating && (
        <div class="absolute top-3 right-3">
          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent-100 text-accent-800 dark:bg-accent-900 dark:text-accent-200">
            ⭐ {restArea.data.rating}
          </span>
        </div>
      )} */}
    </div>

    <div class="p-6">
      <h3 class="text-xl font-semibold text-secondary-900 dark:text-secondary-100 mb-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
        {restArea.data.title}
      </h3>

    <p class="text-secondary-600 dark:text-secondary-400 text-sm mb-3">
      {restArea.data.address_line}
    </p>

    <p class="text-secondary-700 dark:text-secondary-300 mb-4 line-clamp-2">
      {restArea.data.description_short}
    </p>

    <div class="flex items-center justify-between mb-4">
      <span class={`text-sm font-medium ${operatingStatus.class}`}>
        {operatingStatus.status}
      </span>
    </div>

    {keyAmenities.length > 0 && (
      <div class="flex flex-wrap gap-2">
        {keyAmenities.slice(0, 4).map(amenity => (
          <span
            class="inline-flex items-center px-2 py-1 rounded-md text-xs bg-secondary-100 dark:bg-secondary-800 text-secondary-700 dark:text-secondary-300"
            title={amenity.label}
          >
            <span class="mr-1">{amenity.icon}</span>
            {amenity.label}
          </span>
        ))}
        {keyAmenities.length > 4 && (
          <span class="inline-flex items-center px-2 py-1 rounded-md text-xs bg-secondary-100 dark:bg-secondary-800 text-secondary-700 dark:text-secondary-300">
            +{keyAmenities.length - 4} more
          </span>
        )}
      </div>
    )}
  </div>
  </article>
</a>

<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
