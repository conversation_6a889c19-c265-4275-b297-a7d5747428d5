---
import Layout from '../../../layouts/Layout.astro'; // Adjusted path
import { getCollection, render } from 'astro:content';
import AmenityIcon from '../../../components/AmenityIcon.astro'; // Adjusted path
import RestAreaCard from '../../../components/RestAreaCard.astro'; // Adjusted path
import Breadcrumbs from '../../../components/Breadcrumbs.astro'; // Adjusted path
import ParkingBadges from '../../../components/ParkingBadges.astro'; // Adjusted path
import GoogleMap from '../../../components/GoogleMap.astro'; // Adjusted path
import locationsData from '../../../data/locations_pl.json'; // Path is correct
import { generateRestAreaStructuredData, generateBreadcrumbStructuredData } from '../../../utils/structuredData.ts'; // Adjusted path
import { getLangFromUrl, useTranslations } from '../../../i18n/utils';

export async function getStaticPaths() {
  // Fetch Polish entries. IDs are like 'pl/some-area.md'. Slugs are 'pl/some-area'.
  const polishEntries = await getCollection('rest-areas', ({ id }) => id.startsWith('pl/'));
  
  return polishEntries.map((entry) => {
    // URL slug should be the part after 'pl/' and without .md
    // entry.id is 'pl/some-slug.md'
    const slug = entry.id.substring(3).replace(/\.md$/, ''); 
    return {
      params: { slug: slug },
      props: { restArea: entry },
    };
  });
}

const { restArea } = Astro.props; // restArea is a full content collection entry for Polish (e.g. id: 'pl/some-area.md')
const { Content } = await render(restArea);

// Parse work hours
function parseWorkHours(workHours: string) {
  try {
    return JSON.parse(workHours);
  } catch {
    return {};
  }
}

// Parse contact info
function parseContactInfo(contactInfo: string) {
  try {
    return JSON.parse(contactInfo);
  } catch {
    return {};
  }
}

const workHours = parseWorkHours(restArea.data.work_hours);
const contactInfo = parseContactInfo(restArea.data.contact_info || '{}');

// Get current language and translation function
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

// Build breadcrumbs for Polish version - standardized structure
const breadcrumbs = [
  { label: t('nav.home'), href: '/pl/' },
  { label: t('nav.allRestAreas'), href: '/pl/rest-areas/' },
  { label: restArea.data.title, href: `/pl/rest-areas/${Astro.params.slug}` }
];

// Find location details for page title using locations_pl.json
const locationParts = restArea.data.location_path.split('/');
const regionSlugPl = locationParts[1];
const branchSlugPl = locationParts[2];

let regionPl: any = null;
let branchPl: any = null;

if (regionSlugPl && locationsData) {
  regionPl = locationsData.find((r: any) => r.slug === regionSlugPl);
  if (regionPl && branchSlugPl) {
    branchPl = regionPl.children.find((b: any) => b.slug === branchSlugPl);
  }
}


// Get nearby rest areas (same location, Polish content only)
const allPolishRestAreas = await getCollection('rest-areas', ({ id }) => id.startsWith('pl/'));
const nearbyRestAreas = allPolishRestAreas
  .filter((area) => {
    // area.id is 'pl/other-slug.md'
    const areaSlug = area.id.substring(3).replace(/\.md$/, '');
    return areaSlug !== Astro.params.slug && // Compare clean slugs
           area.data.location_path === restArea.data.location_path;
  })
  .slice(0, 3);

const pageTitle = `${restArea.data.title} - ${t('restArea.title')} ${branchPl?.name || regionPl?.name || t('countries.PL')}`;

// Generate structured data
const restAreaUrl = new URL(`/pl/rest-areas/${Astro.params.slug}`, Astro.site).href;
const restAreaStructuredData = generateRestAreaStructuredData(restArea.data, restAreaUrl);
const breadcrumbStructuredData = generateBreadcrumbStructuredData(breadcrumbs);

// Combine structured data
const combinedStructuredData = [restAreaStructuredData, breadcrumbStructuredData];
---

<Layout
  title={pageTitle}
  description={restArea.data.description_short}
  type="place"
  image={restArea.data.featured_image}
  imageAlt={restArea.data.title}
  structuredData={combinedStructuredData}
>
  <main>
    <div class="container-custom pt-8">
      <Breadcrumbs items={breadcrumbs} />

      <!-- Hero Section -->
      <header class="mb-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
          <div>
            <h1 class="text-4xl md:text-5xl font-bold text-secondary-900 dark:text-white mb-4">
              {restArea.data.title}
            </h1>
            <div class="flex flex-wrap items-center gap-4 mb-4">
              <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200">
                {restArea.data.highway_tag.startsWith('A') ? `Autostrada ${restArea.data.highway_tag}` :
                 restArea.data.highway_tag.startsWith('S') ? `Droga Ekspresowa ${restArea.data.highway_tag}` :
                 restArea.data.highway_tag}
              </span>
              {restArea.data.travel_direction && (
                <span class="text-xl text-secondary-600 dark:text-secondary-300">
                  Kierunek: {restArea.data.travel_direction}
                </span>
              )}
            </div>

            <!-- Parking Spaces -->
            <div class="mb-6 mt-8">
              <h3 class="text-lg font-semibold text-secondary-900 dark:text-white mb-3">Miejsca parkingowe</h3>
              <ParkingBadges
                parkingSpacesCars={restArea.data.parking_spaces_cars}
                parkingSpacesTrucks={restArea.data.parking_spaces_trucks}
                parkingSpacesBuses={restArea.data.parking_spaces_buses}
                parkingSpacesDangerous={restArea.data.parking_spaces_dangerous}
              />
            </div>
          </div>

          <div class="relative">
            <GoogleMap
              lat={restArea.data.coordinates.lat}
              lon={restArea.data.coordinates.lon}
              title={restArea.data.title}
              address={restArea.data.address_line}
              className="w-full h-64 lg:h-80 rounded-xl shadow-lg"
              rating={restArea.data.rating}
            />
          </div>
        </div>
      </header>

      <!-- Available Amenities -->
      <section class="mb-12">
        <h2 class="text-2xl font-semibold mb-6">Dostępne udogodnienia</h2>

        <!-- Primary Amenities -->
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-8">
          {(() => {
            const secondaryAmenities = ['fenced_area', 'vehicle_workshop', 'liquid_waste_disposal_rv', 'hydrogen_fueling', 'cng_fueling', 'lng_fueling', 'snow_removal_ramp_trucks', 'cctv', 'lighting', 'car_wash'];
            const amenityOrder = [
              'toilets', 'showers', 'fuel_station', 'ev_charging',
              'restaurant', 'shop', 'playground', 'accommodation', 'wifi', 'security'
            ];
            const orderedAmenities = amenityOrder
              .filter(amenityKey =>
                restArea.data.amenities[amenityKey as keyof typeof restArea.data.amenities] &&
                !secondaryAmenities.includes(amenityKey)
              )
              .map(amenityKey => [amenityKey, restArea.data.amenities[amenityKey as keyof typeof restArea.data.amenities]]);

            return orderedAmenities.map(([amenity, isAvailable]) => (
              <AmenityIcon
                amenityName={amenity as string}
                isPresent={isAvailable as boolean}
                size="md"
                showLabel={true}
                variant="primary"
              />
            ));
          })()}
        </div>
      </section>

      <!-- Secondary Amenities -->
      <section class="mb-12">
        <div class="border-t border-secondary-200 dark:border-secondary-700 pt-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 overflow-hidden">
            {(restArea.data.amenities.fenced_area || restArea.data.security_personnel_on_site || restArea.data.amenities.cctv || restArea.data.amenities.lighting) && (
              <div class="min-w-0">
                <h4 class="text-sm font-semibold text-secondary-600 dark:text-secondary-400 mb-3 uppercase tracking-wide">Bezpieczeństwo</h4>
                <div class="space-y-2">
                  {restArea.data.amenities.fenced_area && <AmenityIcon amenityName="fenced_area" isPresent={true} size="sm" showLabel={true} variant="secondary" />}
                  {restArea.data.security_personnel_on_site && <AmenityIcon amenityName="security_personnel_on_site" isPresent={true} size="sm" showLabel={true} variant="secondary" />}
                  {restArea.data.amenities.cctv && <AmenityIcon amenityName="cctv" isPresent={true} size="sm" showLabel={true} variant="secondary" />}
                  {restArea.data.amenities.lighting && <AmenityIcon amenityName="lighting" isPresent={true} size="sm" showLabel={true} variant="secondary" />}
                </div>
              </div>
            )}
            {(restArea.data.amenities.hydrogen_fueling || restArea.data.amenities.cng_fueling || restArea.data.amenities.lng_fueling) && (
              <div class="min-w-0">
                <h4 class="text-sm font-semibold text-secondary-600 dark:text-secondary-400 mb-3 uppercase tracking-wide">Paliwa alternatywne</h4>
                <div class="space-y-2">
                  {restArea.data.amenities.hydrogen_fueling && <AmenityIcon amenityName="hydrogen_fueling" isPresent={true} size="sm" showLabel={true} variant="secondary" />}
                  {restArea.data.amenities.cng_fueling && <AmenityIcon amenityName="cng_fueling" isPresent={true} size="sm" showLabel={true} variant="secondary" />}
                  {restArea.data.amenities.lng_fueling && <AmenityIcon amenityName="lng_fueling" isPresent={true} size="sm" showLabel={true} variant="secondary" />}
                </div>
              </div>
            )}
            {(restArea.data.amenities.vehicle_workshop || restArea.data.amenities.liquid_waste_disposal_rv || restArea.data.amenities.snow_removal_ramp_trucks || restArea.data.amenities.car_wash) && (
              <div class="min-w-0">
                <h4 class="text-sm font-semibold text-secondary-600 dark:text-secondary-400 mb-3 uppercase tracking-wide">Usługi specjalistyczne</h4>
                <div class="space-y-2">
                  {restArea.data.amenities.vehicle_workshop && <AmenityIcon amenityName="vehicle_workshop" isPresent={true} size="sm" showLabel={true} variant="secondary" />}
                  {restArea.data.amenities.liquid_waste_disposal_rv && <AmenityIcon amenityName="liquid_waste_disposal_rv" isPresent={true} size="sm" showLabel={true} variant="secondary" />}
                  {restArea.data.amenities.snow_removal_ramp_trucks && <AmenityIcon amenityName="snow_removal_ramp_trucks" isPresent={true} size="sm" showLabel={true} variant="secondary" />}
                  {restArea.data.amenities.car_wash && <AmenityIcon amenityName="car_wash" isPresent={true} size="sm" showLabel={true} variant="secondary" />}
                </div>
              </div>
            )}
            {restArea.data.toilets_accessible && (
              <div class="min-w-0">
                <h4 class="text-sm font-semibold text-secondary-600 dark:text-secondary-400 mb-3 uppercase tracking-wide">Dostępność</h4>
                <div class="space-y-2">
                  <AmenityIcon amenityName="toilets_accessible" isPresent={true} size="sm" showLabel={true} variant="secondary" />
                </div>
              </div>
            )}
          </div>
        </div>
      </section>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <aside class="space-y-6 order-1 lg:order-2">
          {restArea.data.maps_url && (
            <div class="card p-6">
              <h3 class="text-xl font-semibold mb-4">Lokalizacja</h3>
              <a href={restArea.data.maps_url} target="_blank" rel="noopener noreferrer" class="btn-primary w-full text-center">
                Zobacz na mapie
              </a>
            </div>
          )}
          <div class="card p-6">
            <h3 class="text-xl font-semibold mb-4">Szybkie informacje</h3>
            <div class="space-y-3">
              <div>
                <span class="text-sm font-medium text-secondary-600 dark:text-secondary-400">Adres:</span>
                <p class="text-secondary-900 dark:text-secondary-100">{restArea.data.address_line}</p>
              </div>
              {restArea.data.administrator && (
                <div>
                  <span class="text-sm font-medium text-secondary-600 dark:text-secondary-400">Operator:</span>
                  <p class="text-secondary-900 dark:text-secondary-100">{restArea.data.administrator}</p>
                </div>
              )}
              {restArea.data.mop_category && (
                <div>
                  <span class="text-sm font-medium text-secondary-600 dark:text-secondary-400">Kategoria:</span>
                  <p class="text-secondary-900 dark:text-secondary-100">{restArea.data.mop_category}</p>
                </div>
              )}
            </div>
          </div>
          {Object.keys(workHours).length > 0 && (
            <div class="card p-6">
              <h3 class="text-xl font-semibold mb-4">Godziny otwarcia</h3>
              <div class="space-y-2">
                {Object.entries(workHours).map(([day, hours]) => (
                  <div class="flex justify-between">
                    <span class="capitalize text-secondary-600 dark:text-secondary-400">{day}:</span>
                    <span class="text-secondary-900 dark:text-secondary-100">{hours as string}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
          {false && Object.keys(contactInfo).length > 0 && (
            <div class="card p-6">
              <h3 class="text-xl font-semibold mb-4">Kontakt</h3>
              <div class="space-y-2">
                {contactInfo.phone && ( <div> <span class="text-sm font-medium text-secondary-600 dark:text-secondary-400">Telefon:</span> <p class="text-secondary-900 dark:text-secondary-100">{contactInfo.phone}</p> </div> )}
                {contactInfo.email && ( <div> <span class="text-sm font-medium text-secondary-600 dark:text-secondary-400">Email:</span> <p class="text-secondary-900 dark:text-secondary-100">{contactInfo.email}</p> </div> )}
              </div>
            </div>
          )}
        </aside>
        <article class="lg:col-span-2 order-2 lg:order-1">
          <div class="prose prose-lg dark:prose-invert max-w-none [&>p]:mb-6">
            <Content />
          </div>
        </article>
      </div>

      {nearbyRestAreas.length > 0 && (
        <section class="mt-12">
          <h2 class="text-2xl font-semibold mb-6">
            Pobliskie MOP-y w {branchPl?.name || regionPl?.name || 'okolicy'}
          </h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {nearbyRestAreas.map((area: any) => (
              <RestAreaCard restArea={area} />
            ))}
          </div>
        </section>
      )}
    </div>
  </main>
  <div class="pb-12"></div>
</Layout>
