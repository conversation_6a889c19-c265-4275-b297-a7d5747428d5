---
import Layout from '../../../layouts/Layout.astro';
import { getCollection } from 'astro:content';
import RestAreaCard from '../../../components/RestAreaCard.astro';

import Breadcrumbs from '../../../components/Breadcrumbs.astro';
import locationsData from '../../../data/locations_pl.json';
import { generateLocationStructuredData, generateBreadcrumbStructuredData } from '../../../utils/structuredData.ts';
import { getLangFromUrl, useTranslations } from '../../../i18n/utils';

export async function getStaticPaths() {
  const paths: any[] = [];

  // Sort locations alphabetically by name for consistent path generation
  const sortedLocationsData = [...locationsData].sort((a, b) => a.name.localeCompare(b.name));

  // Generate paths for all locations
  sortedLocationsData.forEach((region: any) => {
    // Add region path
    paths.push({
      params: { locationSlug: region.slug },
      props: {
        currentLocation: region,
        locationPath: [region],
        isLeaf: region.children.length === 0
      }
    });

    // Add branch paths (sort children alphabetically)
    [...region.children].sort((a: any, b: any) => a.name.localeCompare(b.name)).forEach((branch: any) => {
      paths.push({
        params: { locationSlug: `${region.slug}/${branch.slug}` },
        props: {
          currentLocation: branch,
          locationPath: [region, branch],
          isLeaf: true
        }
      });
    });
  });

  return paths;
}

const { currentLocation, locationPath, isLeaf } = Astro.props;

// Get current language and translations
const currentLang = getLangFromUrl(Astro.url);
const t = useTranslations(currentLang);

// Get Polish rest area entries. IDs are like 'pl/some-area.md'.
const polishRestAreas = await getCollection('rest-areas', ({ id }) => id.startsWith('pl/'));

// Filter rest areas for current location
// For regions, show all rest areas that belong to the region
const locationRestAreas = polishRestAreas.filter((area) => {
  const areaLocationPath = area.data.location_path;
  // For Polish pages, location_path format is "polska/region/city" or "poland/region/city"
  // We want to match the region slug in the location path
  return areaLocationPath.includes(`/${currentLocation.slug}/`) ||
         areaLocationPath.includes(`${currentLocation.slug}/`) ||
         areaLocationPath.endsWith(`/${currentLocation.slug}`);
});

// Build breadcrumbs
const breadcrumbs = [
  { label: t('nav.home'), href: '/pl/' },
  { label: t('countries.PL'), href: '/pl/polska/' },
  ...locationPath.map((loc: any, index: number) => ({
    label: loc.name,
    href: index === 0 ? `/pl/polska/${loc.slug}/` : `/pl/polska/${locationPath[0].slug}/${loc.slug}/`
  }))
];

const pageTitle = `Miejsca odpoczynku w ${currentLocation.name}, Polska`;
const pageDescription = `Znajdź miejsca odpoczynku na autostradach w ${currentLocation.name}, Polska. Przeglądaj ${locationRestAreas.length} miejsc odpoczynku ze szczegółowymi informacjami o udogodnieniach.`;

// Generate structured data
const locationUrl = new URL(Astro.url.pathname, Astro.site).href;
const parentLocation = locationPath.length > 1 ? locationPath[0] : undefined;
const locationStructuredData = generateLocationStructuredData(
  currentLocation,
  locationUrl,
  locationRestAreas.length,
  parentLocation
);
const breadcrumbStructuredData = generateBreadcrumbStructuredData(breadcrumbs);

// Combine structured data
const combinedStructuredData = [locationStructuredData, breadcrumbStructuredData];
---

<Layout
  title={pageTitle}
  description={pageDescription}
  type="place"
  structuredData={combinedStructuredData}
>
  <main>
    <div class="container-custom pt-8">
      <Breadcrumbs items={breadcrumbs} />

      <div class="mb-8">
        <h1 class="text-4xl md:text-5xl font-bold text-secondary-900 dark:text-white mb-4">
          Miejsca odpoczynku w {currentLocation.name}, Polska
        </h1>
        <p class="text-xl text-secondary-600 dark:text-secondary-300">
          {isLeaf ? (
            `Odkryj ${locationRestAreas.length} miejsc odpoczynku na autostradach w ${currentLocation.name}`
          ) : (
            `Przeglądaj lokalizacje i miejsca odpoczynku w regionie ${currentLocation.name}`
          )}
        </p>
      </div>

      {/* Child locations section removed - now directly showing rest areas for regions */}

      {locationRestAreas.length > 0 ? (
        <!-- Show rest areas -->
        <section class="mb-16">
          <h2 class="text-2xl font-semibold mb-6">
            Miejsca odpoczynku w regionie {currentLocation.name}
          </h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {locationRestAreas.map((restArea) => {
              // Generate hierarchical URL for this rest area
              const cleanSlug = restArea.id.substring(3).replace(/\.md$/, ''); // Remove 'pl/' prefix and .md
              const locationParts = restArea.data.location_path.replace(/^poland\//, '');
              const hierarchicalUrl = `/pl/polska/${locationParts}/${cleanSlug}`;

              return (
                <RestAreaCard
                  restArea={restArea}
                  hierarchicalUrl={hierarchicalUrl}
                />
              );
            })}
          </div>
        </section>
      ) : isLeaf ? (
        <!-- No rest areas found -->
        <section class="text-center py-12 mb-16">
          <div class="max-w-md mx-auto">
            <h2 class="text-2xl font-semibold text-secondary-900 dark:text-white mb-4">
              Nie znaleziono miejsc odpoczynku
            </h2>
            <p class="text-secondary-600 dark:text-secondary-400 mb-6">
              Nie mamy jeszcze żadnych miejsc odpoczynku dla {currentLocation.name}.
            </p>
            <a href="/pl/polska/" class="btn-primary">
              Przeglądaj inne lokalizacje
            </a>
          </div>
        </section>
      ) : null}
    </div>
  </main>
</Layout>
